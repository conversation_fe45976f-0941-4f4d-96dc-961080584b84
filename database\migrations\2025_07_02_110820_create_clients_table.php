<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Rdv extends Model
{
    protected $fillable = [
        'date', 
        'time', 
        'status', 
        'etape', 
        'created_by', 
        'car_id',
    ];

    protected $casts = [
        'date' => 'date',
        'time' => 'datetime:H:i',
    ];

    public function car()
    {
        return $this->belongsTo(Car::class);
    }

    public function client()
    {
        return $this->hasOneThrough(Client::class, Car::class, 'id', 'id', 'car_id', 'client_id');
    }

    public function photoAv()
    {
        return $this->hasMany(PhotoAv::class);
    }

    public function photoAp()
    {
        return $this->hasMany(PhotoAp::class);
    }

    public function files()
    {
        return $this->hasMany(File::class);
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function etapeModel()
    {
        return $this->belongsTo(Etape::class, 'etape');
    }

    // Scopes للبحث حسب الحالة
    public function scopeEnAttente($query)
    {
        return $query->where('status', 'En attente');
    }

    public function scopeFixe($query)
    {
        return $query->where('status', 'Fixé');
    }

    public function scopeImmediat($query)
    {
        return $query->where('status', 'Immediat');
    }

    public function scopeAnnule($query)
    {
        return $query->where('status', 'annuler');
    }
}

